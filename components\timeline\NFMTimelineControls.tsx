/**
 * NFM Timeline Controls - Control panel for the timeline editor
 */

import React, { useCallback, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { InteractionMode, NFMTimelineControlsProps } from '@/types/timelineEditor';
import { useVideoTimeline } from '@/components/contexts/VideoTimelineContext';
import {
  Play,
  Pause,
  Plus,
  Minus,
  SkipForward,
  SkipBack,
  Edit3,
  Lock
} from 'lucide-react';
import PlaybackSpeedButton from '../ui_custom/playback-speed-button';
import { Doc } from '@/convex/_generated/dataModel';

export function NFMTimelineControls({
  onZoom,
  interactionMode = InteractionMode.VIEW, // Default to editing disabled
  setInteractionMode,
  events,
  modalities,
  className
}: NFMTimelineControlsProps) {
  // Use VideoTimelineContext for unified state management
  const videoTimeline = useVideoTimeline();

  // Handle play/pause toggle using context
  const handlePlayPause = useCallback(() => {
    videoTimeline.togglePlayPause();
  }, [videoTimeline]);

   const handleSetInteractionMode = useCallback((enabled: boolean) => {
    setInteractionMode?.(enabled ? InteractionMode.EDIT : InteractionMode.VIEW)
  }, [setInteractionMode])

  // Use VideoTimelineContext for unified event navigation
  const handlePrevEventWithScroll = useCallback(() => {
    const success = videoTimeline.goToPrevEvent();
    if (!success) {
      console.debug('[NFMTimelineControls] No previous event found');
    }
  }, [videoTimeline]);

  const handleNextEventWithScroll = useCallback(() => {
    const success = videoTimeline.goToNextEvent();
    if (!success) {
      console.debug('[NFMTimelineControls] No next event found');
    }
  }, [videoTimeline]);

  // Check if navigation buttons should be disabled using VideoTimelineContext
  const hasPrevEvent = useMemo(() => {
    if (!events || events.length === 0) return false;
    return events.some((event: Doc<"monitoringEvents">) => event.startTime < videoTimeline.currentTime);
  }, [events, videoTimeline.currentTime]);

  const hasNextEvent = useMemo(() => {
    console.debug('[NFMTimelineControls] Checking for next event');
    if (!events || events.length === 0) return false;
    return events.some((event: Doc<"monitoringEvents">) => event.startTime > videoTimeline.currentTime);
  }, [events, videoTimeline.currentTime]);

  // Handle playback speed change using VideoTimelineContext
  const handlePlaybackSpeedChange = useCallback((speed: number) => {
    videoTimeline.setPlaybackRate(speed);
  }, [videoTimeline]);
  
  return (
    // Enhanced centered controls with proper event navigation and playback speed
    <div className={cn(
      'flex items-center justify-center gap-2 p-3 bg-background border-b border-gray-200 dark:border-gray-700',
      className
    )}>
      {/* Previous Event */}
      <Button
        variant="outline"
        size="sm"
        onClick={handlePrevEventWithScroll}
        disabled={!hasPrevEvent}
        title={hasPrevEvent ? "Previous Event" : "No previous events"}
        className="h-8 w-8 p-0"
      >
        <SkipBack className="h-4 w-4" />
      </Button>

      {/* Play/Pause */}
      <Button
        variant={videoTimeline.isPlaying ? "default" : "outline"}
        size="sm"
        onClick={handlePlayPause}
        title={videoTimeline.isPlaying ? "Pause" : "Play"}
        className="h-8 w-8 p-0"
      >
        {videoTimeline.isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
      </Button>

      {/* Next Event */}
      <Button
        variant="outline"
        size="sm"
        onClick={handleNextEventWithScroll}
        disabled={!hasNextEvent}
        title={hasNextEvent ? "Next Event" : "No upcoming events"}
        className="h-8 w-8 p-0"
      >
        <SkipForward className="h-4 w-4" />
      </Button>

      {/* Divider */}
      <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

      {/* Zoom Out (Fixed: Minus button now zooms OUT) */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => videoTimeline.setTimelineScale?.(1.2)}
        title="Zoom Out (-)"
        className="h-8 w-8 p-0"
      >
        <Minus className="h-4 w-4" />
      </Button>

      {/* Zoom In (Fixed: Plus button now zooms IN) */}
      <Button
        variant="outline"
        size="sm"
        onClick={() => onZoom?.(0.83)}
        title="Zoom In (+)"
        className="h-8 w-8 p-0"
      >
        <Plus className="h-4 w-4" />
      </Button>

      {/* Divider */}
      <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

      {/* Playback Speed Control */}
      <PlaybackSpeedButton
        startSpeed={videoTimeline.playbackRate}
        onSpeedChange={handlePlaybackSpeedChange}
        showTooltip={true}
        className="h-8"
      />

      {/* Divider */}
      <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2" />

      {/* Allow Editing Toggle */}
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1">
          {interactionMode === InteractionMode.EDIT ? (
            <Edit3 className="h-4 w-4 text-blue-600" />
          ) : (
            <Lock className="h-4 w-4 text-gray-500" />
          )}
        </div>
        <Switch
          checked={interactionMode === InteractionMode.EDIT}
          onCheckedChange={handleSetInteractionMode}
          aria-label="Allow editing"
        />
        <Label
          htmlFor="allow-editing"
          className="text-sm font-medium cursor-pointer"
          //onClick={() => setInteractionMode?.(interactionMode === InteractionMode.EDIT ? InteractionMode.VIEW : InteractionMode.EDIT)}
        >
          {interactionMode === InteractionMode.EDIT ? 'Editing' : 'Locked'}
        </Label>
      </div>
    </div>
  );
}


