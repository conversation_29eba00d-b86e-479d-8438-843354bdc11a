/**
 * Type definitions for the react-timeline-editor integration with NFM
 * These types extend and complement the base timeline editor types
 */

import { TimelineRow, TimelineEvent, TimelineEffect, TimelineState } from '@/components/timeline/interface/timeline';
import { Doc, Id } from '@/convex/_generated/dataModel';

// NFMTimelineEvent removed - using unified TimelineEvent from components/timeline/interface/timeline
/**
 * Timeline interaction modes for better type safety
 */
export enum InteractionMode {
  VIEW = 'view',
  EDIT = 'edit',
  DISABLED = 'disabled'
}
/**
 * Extended timeline row with NFM-specific properties
 */
export interface NFMTimelineRow extends TimelineRow {
  // Original modality data
  originalModality?: Doc<"modalityConfigs">;
  // Display properties
  displayName: string;
  color?: string;
  // Interaction properties
  visible?: boolean;
  locked?: boolean;
  // Navigation helpers
  eventCount?: number;
}

/**
 * NFM-specific timeline effect with enhanced capabilities
 */
export interface NFMTimelineEffect extends TimelineEffect {
  // Visual properties
  color?: string;
  icon?: string;
  // Behavior properties
  category?: 'modality' | 'event' | 'system';
  priority?: number;
  // Rendering properties
  customRenderer?: boolean;
}

/**
 * Timeline editor configuration for NFM
 */
export interface NFMTimelineConfig {
  // Display settings
  showGrid?: boolean;
  snapToGrid?: boolean;
  gridInterval?: number; // seconds
  
  // Zoom settings
  minScale?: number;
  maxScale?: number;
  defaultScale?: number;
  
  // Interaction settings
  allowDrag?: boolean;
  allowResize?: boolean;
  allowCreate?: boolean;
  allowDelete?: boolean;
  
  // Visual settings
  rowHeight?: number;
  cursorColor?: string;
  selectionColor?: string;
  
  // Performance settings
  virtualizeRows?: boolean;
  maxVisibleRows?: number;
  
  // Feature flags
  enableKeyboardShortcuts?: boolean;
  enableContextMenu?: boolean;
  enableTooltips?: boolean;
}

/**
 * Timeline editor state management
 */
export interface NFMTimelineState {
  // Timeline data
  editorData: NFMTimelineRow[];
  effects: Record<string, NFMTimelineEffect>;

  // Playback state
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  playbackRate: number;

  // View state
  scale: number;
  scrollLeft: number;
  scrollTop: number;
  selectedActions: string[];
  selectedRows: string[];

  // Edit state
  isDirty: boolean;
  lastSaved: Date | null;
  undoStack: NFMTimelineRow[][];
  redoStack: NFMTimelineRow[][];

  // UI state
  showControls: boolean;
  expandedView: boolean;
  visibleModalities: string[];
}

/**
 * Unified Video-Timeline State Management
 * Single source of truth for video and timeline synchronization
 */
export interface VideoTimelineState {
  // Core playback state
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  playbackRate: number;

  // Seeking state management
  isSeeking: boolean;
  seekingTime: number | null;
  seekingSource: 'video' | 'timeline' | null;

  // Loading states
  isVideoLoading: boolean;
  isTimelineLoading: boolean;

  // Error states
  videoError: string | null;
  timelineError: string | null;

  // NEW: Event navigation state
  currentEventId: string | null;
  timelineScale: number;
  isTimelineDragging: boolean;
}

/**
 * Video-Timeline Actions Interface
 */
export interface VideoTimelineActions {
  // Playback controls
  play: () => void;
  pause: () => void;
  togglePlayPause: () => void;

  // Seeking controls
  seekTo: (time: number, source: 'video' | 'timeline') => void;
  startSeeking: (time: number, source: 'video' | 'timeline') => void;
  updateSeeking: (time: number) => void;
  endSeeking: () => void;

  // Playback rate controls
  setPlaybackRate: (rate: number) => void;

  // Duration management
  setDuration: (duration: number) => void;

  // Loading state management
  setVideoLoading: (loading: boolean) => void;
  setTimelineLoading: (loading: boolean) => void;

  // Error management
  setVideoError: (error: string | null) => void;
  setTimelineError: (error: string | null) => void;

  // Internal time updates (for synchronization)
  updateCurrentTime: (time: number, source: 'video' | 'timeline') => void;

  // NEW: Event navigation actions
  goToNextEvent: () => boolean;
  goToPrevEvent: () => boolean;
  goToEvent: (eventId: string) => boolean;
  goToNextEventInModality: (modalityId: string) => boolean;
  goToPrevEventInModality: (modalityId: string) => boolean;
  goToTime: (time: number) => void; // Safe seeking method

  // NEW: Timeline interaction actions
  setTimelineScale: (scale: number) => void;
  setTimelineDragging: (isDragging: boolean) => void;


}

/**
 * Combined Video-Timeline Context Type
 */
export interface VideoTimelineContextType extends VideoTimelineState, VideoTimelineActions {
  // Additional context-specific properties
  isInitialized: boolean;
}

/**
 * Timeline editor event handlers
 */
export interface NFMTimelineHandlers {
  // Data change handlers
  onDataChange?: (data: NFMTimelineRow[]) => void;
  onEventCreate?: (event: TimelineEvent,modalityId: string ) => void;
  onEventUpdate?: (event: TimelineEvent) => void;
  onEventDelete?: (actionId: string) => void;
  
  // Selection handlers
  onEventSelect?: (actionIds: string[]) => void;
  onRowSelect?: (rowIds: string[]) => void;
  
  // Playback handlers
  onTimeChange?: (time: number) => void;
  onPlayStateChange?: (isPlaying: boolean) => void;
  onPlaybackRateChange?: (rate: number) => void;
  
  // View handlers
  onScaleChange?: (scale: number) => void;
  onScrollChange?: (scrollLeft: number, scrollTop: number) => void;
  
  // Interaction handlers
  onEventClick?: (event: TimelineEvent, e: React.MouseEvent) => void;
  onEventDoubleClick?: (event: TimelineEvent, e: React.MouseEvent) => void;
  onEventContextMenu?: (event: TimelineEvent, e: React.MouseEvent) => void;
  onRowClick?: (row: NFMTimelineRow, event: React.MouseEvent) => void;
  onTimelineClick?: (time: number, rowId?: string) => void;
  
  // Error handlers
  onError?: (error: Error) => void;
  onValidationError?: (errors: string[]) => void;
}

/**
 * Timeline editor props combining all configuration
 */
export interface NFMTimelineEditorProps extends NFMTimelineHandlers {
  // Core data
  modalities: Doc<"modalityConfigs">[];
  visibleModalities?: Doc<"modalityConfigs">[];
  events: Doc<"monitoringEvents">[];

  // Playback sync
  currentTime?: number;
  duration?: number;
  isPlaying?: boolean;

  // Scale control
  scale?: number;
  onScaleChange?: (zoomDelta: number) => void;

  // Configuration
  config?: Partial<NFMTimelineConfig>;
  //readOnly?: boolean;
  //disabled?: boolean;
  //allowEditing?: boolean;

  // Styling
  className?: string;
  style?: React.CSSProperties;
  height?: number;

  // Interaction mode - single source of truth for timeline behavior
  interactionMode?: InteractionMode;

  // Timeline engine callback
  onTimelineEngineReady?: (engine: import('@/components/timeline/interface/timeline').TimelineState) => void;



  // Integration props
  //projectId?: Id<"projects">;
  //sessionId?: Id<"streamSessions">;
}

/**
 * Timeline control panel props
 */
export interface NFMTimelineControlsProps {
  // Timeline state (DEPRECATED - now from VideoTimelineContext)
  /** @deprecated Use VideoTimelineContext.currentTime */
  //currentTime: number;
  /** @deprecated Use VideoTimelineContext.duration */
  //duration: number;
  /** @deprecated Use VideoTimelineContext.isPlaying */
  //isPlaying: boolean;

  // Control handlers (DEPRECATED - now from VideoTimelineContext)
  /** @deprecated Use VideoTimelineContext.play */
  //onPlay?: () => void;
  /** @deprecated Use VideoTimelineContext.pause */
  //onPause?: () => void;
  onZoom?: (value: number) => void;
  /** @deprecated */
  //onZoomIn?: () => void;
  /** @deprecated */
 // onZoomOut?: () => void;

  // Navigation handlers (DEPRECATED - now from VideoTimelineContext)
  /** @deprecated Use VideoTimelineContext.goToPrevEvent */
 // onPrevEvent?: () => void;
  /** @deprecated Use VideoTimelineContext.goToNextEvent */
//  onNextEvent?: () => void;

  // Playback speed (DEPRECATED - now from VideoTimelineContext)
  /** @deprecated Use VideoTimelineContext.playbackRate */
 // playbackRate?: number;
  /** @deprecated Use VideoTimelineContext.setPlaybackRate */
  //onPlaybackRateChange?: (rate: number) => void;

  // Auto-scroll for event navigation (DEPRECATED - now from VideoTimelineContext)
  /** @deprecated Use VideoTimelineContext.goToTime */
  //onScrollToTime?: (time: number) => void;

  // Editing control
  interactionMode?: InteractionMode;
  setInteractionMode?: (mode: InteractionMode) => void;

  // Data
  modalities?: Doc<"modalityConfigs">[];
  events?: Doc<"monitoringEvents">[];

  // Styling
  className?: string;
}

/**
 * Custom action renderer props
 */
export interface NFMActionRendererProps {
  action: TimelineEvent;
  row: NFMTimelineRow;
  isSelected: boolean;
  isHovered: boolean;
  scale: number;
  height: number;
  onClick?: (event: React.MouseEvent) => void;
  onDoubleClick?: (event: React.MouseEvent) => void;
  onContextMenu?: (event: React.MouseEvent) => void;
}

/**
 * Timeline data transformation result
 */
export interface TimelineDataTransformResult {
  editorData: TimelineRow[];
  effects: Record<string, NFMTimelineEffect>;
  duration: number;
  validation: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  };
}

/**
 * Timeline change detection result
 */
export interface TimelineChangeResult {
  hasChanges: boolean;
  createdEvents: Partial<TimelineEvent>[];
  updatedEvents: Partial<TimelineEvent>[];
  deletedEventIds: string[];
  movedEvents: Array<{
    eventId: string;
    fromModalityId: string;
    toModalityId: string;
  }>;
}

/**
 * Timeline export/import formats
 */
export interface TimelineExportData {
  version: string;
  timestamp: string;
  projectId: string;
  sessionId: string;
  modalities: Doc<"modalityConfigs">[];
  events: TimelineEvent[];
  metadata: {
    duration: number;
    eventCount: number;
    modalityCount: number;
  };
}

/**
 * Timeline performance metrics
 */
export interface TimelinePerformanceMetrics {
  renderTime: number;
  updateTime: number;
  memoryUsage: number;
  eventCount: number;
  visibleEventCount: number;
  fps: number;
}

/**
 * Timeline keyboard shortcuts
 */
export interface TimelineKeyboardShortcuts {
  play: string;
  pause: string;
  seekForward: string;
  seekBackward: string;
  zoomIn: string;
  zoomOut: string;
  fitToView: string;
  selectAll: string;
  delete: string;
  undo: string;
  redo: string;
  copy: string;
  paste: string;
}
